-- 野外入侵系统测试脚本
-- 使用方法：在游戏控制台中执行 dofile("mods/thinking/test_invasion_system.lua")

local function TestInvasionSystem()
    print("=== 野外入侵系统测试 ===")
    
    if not TheWorld.ismastersim then
        print("❌ 必须在服务端运行此测试")
        return
    end
    
    local invasion_manager = TheWorld.components.season_warden_invasion
    if not invasion_manager then
        print("❌ 未找到入侵管理器组件")
        return
    end
    
    print("✓ 找到入侵管理器组件")
    
    -- 1. 测试基本配置
    print("\n--- 基本配置测试 ---")
    print("入侵开启状态:", invasion_manager.enabled)
    print("每季入侵次数:", invasion_manager.per_season)
    print("重试间隔天数:", invasion_manager.respawn_days)
    print("预警时间(秒):", invasion_manager.warn_secs)
    print("战斗半径:", invasion_manager.battle_radius)
    print("血量倍率:", invasion_manager.hp_mul)
    print("掉落倍率:", invasion_manager.loot_mul)
    
    -- 2. 测试多人缩放计算
    print("\n--- 多人缩放测试 ---")
    local player_count = #(AllPlayers or {})
    local multiplayer_scale = player_count > 1 and math.sqrt(player_count) or 1.0
    print("当前玩家数:", player_count)
    print("多人缩放倍率:", string.format("%.2f", multiplayer_scale))
    print("缩放后血量:", math.floor(8000 * invasion_manager.hp_mul * multiplayer_scale))
    print("缩放后掉落:", math.max(1, math.floor(0.5 * 2 * multiplayer_scale + 0.5)))
    
    -- 3. 测试位置检测功能
    print("\n--- 位置检测测试 ---")
    local player = ThePlayer or AllPlayers[1]
    if player then
        local pos = player:GetPosition()
        print("玩家位置:", string.format("(%.1f, %.1f)", pos.x, pos.z))
        
        -- 测试当前位置是否适合入侵
        local x, y, z = pos:Get()
        
        -- 检查基地建筑
        local structures = TheSim:FindEntities(x, y, z, 20, nil, {"INLIMBO", "NOCLICK", "FX"})
        local structure_count = 0
        for _, ent in ipairs(structures) do
            if ent.components and (ent.components.workable or ent.components.container or ent.components.burnable) then
                if ent:HasTag("structure") or ent.prefab == "campfire" or ent.prefab == "firepit" or 
                   ent.prefab == "coldfire" or ent.prefab == "coldfirepit" or ent.prefab == "treasurechest" or
                   ent.prefab == "icebox" or ent.prefab == "saltbox" then
                    structure_count = structure_count + 1
                end
            end
        end
        print("20格内建筑数量:", structure_count)
        print("是否基地密集区域:", structure_count >= 3 and "是" or "否")
        
        -- 检查特殊建筑
        local special_entities = TheSim:FindEntities(x, y, z, 5, nil, {"INLIMBO", "NOCLICK", "FX"})
        local special_found = {}
        for _, ent in ipairs(special_entities) do
            if ent.prefab == "cave_entrance" or ent.prefab == "cave_entrance_open" or ent.prefab == "cave_entrance_ruins" then
                table.insert(special_found, "洞穴入口")
            elseif ent.prefab == "teleportato_base" or ent.prefab == "teleportato_potato" or 
                   ent.prefab == "teleportato_ring" or ent.prefab == "teleportato_box" or
                   ent.prefab == "teleportato_crank" or ent.prefab == "teleportato_sw_base" then
                table.insert(special_found, "传送台")
            elseif ent.prefab == "multiplayer_portal" or ent.prefab == "multiplayer_portal_moonrock" or
                   ent.prefab == "moonbase" or ent.prefab == "portal_entrance" then
                table.insert(special_found, "传送门")
            end
        end
        print("5格内特殊建筑:", #special_found > 0 and table.concat(special_found, ", ") or "无")
        
        local suitable = structure_count < 3 and #special_found == 0
        print("当前位置适合入侵:", suitable and "是" or "否")
    else
        print("❌ 未找到玩家")
    end
    
    -- 4. 测试当前状态
    print("\n--- 当前状态测试 ---")
    print("本季已完成入侵次数:", invasion_manager.invasions_done)
    print("入侵激活状态:", invasion_manager.active and "激活" or "未激活")
    print("Boss存在状态:", invasion_manager.boss and invasion_manager.boss:IsValid() and "存在" or "不存在")
    print("上次失败日期:", invasion_manager.last_failed_day or "无")
    
    local current_day = math.floor(GetTime() / (TUNING.TOTAL_DAY_TIME or 480))
    print("当前游戏日期:", current_day)
    
    if invasion_manager.last_failed_day and invasion_manager.last_failed_day == current_day then
        print("⚠️ 今日已有入侵失败，不会再次入侵")
    end
    
    -- 5. 手动触发入侵测试（仅在未激活时）
    print("\n--- 手动触发测试 ---")
    if not invasion_manager.active and invasion_manager.invasions_done < invasion_manager.per_season then
        print("可以手动触发入侵测试")
        print("执行命令: c_exec(TheWorld.components.season_warden_invasion:TryStartInvasion())")
    else
        if invasion_manager.active then
            print("⚠️ 入侵已激活，无法重复触发")
        elseif invasion_manager.invasions_done >= invasion_manager.per_season then
            print("⚠️ 本季入侵次数已达上限")
        end
    end
    
    -- 6. 重置测试（危险操作）
    print("\n--- 重置选项 ---")
    print("重置本季入侵次数: c_exec(TheWorld.components.season_warden_invasion.invasions_done = 0)")
    print("重置同日限制: c_exec(TheWorld.components.season_warden_invasion.last_failed_day = nil)")
    print("强制触发入侵: c_exec(TheWorld.components.season_warden_invasion:TryStartInvasion())")
    
    print("\n=== 测试完成 ===")
end

-- 执行测试
TestInvasionSystem()
